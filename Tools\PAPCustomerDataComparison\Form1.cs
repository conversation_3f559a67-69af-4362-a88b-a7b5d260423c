﻿using Aspose.Cells;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PAPCustomerDataComparison
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        // 选择IQC数据文件夹按钮
        private void button1_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                IQCFilesBrowserPath.Text = dialog.SelectedPath;
            }
        }

        // 选择APMT数据文件夹按钮
        private void button5_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                APMTFolderPath.Text = dialog.SelectedPath;
            }
        }

        // 选择PCD数据文件夹按钮
        private void button6_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                PCDFolderPath.Text = dialog.SelectedPath;
            }
        }

        // 选择MES数据文件夹按钮
        private void button7_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                MESFolderPath.Text = dialog.SelectedPath;
            }
        }

        // 选择IPQC数据文件夹按钮
        private void button8_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                IPQCFolderPath.Text = dialog.SelectedPath;
            }
        }

        // 选择Result模板文件夹按钮
        private void button2_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                ResultTemplateFolderPath.Text = dialog.SelectedPath;
            }
        }

        // 选择Result输出文件夹按钮
        private void button3_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择文件夹";
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                ResultOutputFolderPath.Text = dialog.SelectedPath;
            }
        }

        // 对比执行按钮
        private void button4_Click1(object sender, EventArgs e)
        {
            // 设置许可证
            new Aspose.Cells.License().SetLicense("Aspose.Total.NET.lic");

            // 获取文件路径
            string IQCFolderPath = IQCFilesBrowserPath.Text;// IQC文件夹路径
            string IPQCDataFolderPath = IPQCFolderPath.Text;// IPQC文件夹路径
            string APMTDataFolderPath = APMTFolderPath.Text; // APMT文件夹路径
            string PCDDataFolderPath = PCDFolderPath.Text; // PCD文件夹路径
            string MESDataFolderPath = MESFolderPath.Text; // MES文件夹路径
            string ResultPath = ResultTemplateFolderPath.Text;// Result模板路径
            string ResultOutputPath = ResultOutputFolderPath.Text;// Result输出路径

            // 校验右侧必选项
            if (string.IsNullOrEmpty(ResultPath) || string.IsNullOrEmpty(ResultOutputPath))
            {
                MessageBox.Show("请先选择Result模板文件夹和Result输出文件夹！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 校验左侧至少选一项
            if (string.IsNullOrEmpty(IQCFolderPath) && string.IsNullOrEmpty(APMTDataFolderPath) && string.IsNullOrEmpty(PCDDataFolderPath) && string.IsNullOrEmpty(MESDataFolderPath))
            {
                MessageBox.Show("请至少选择IQC文件夹路径或APMT文件夹路径或PCD文件夹路径或MES文件夹路径中的一项！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 校验IQC文件夹路径和文件格式（只能是xls，xlsx后缀）
            if (!string.IsNullOrEmpty(IQCFolderPath))
            {
                if (!CheckFolderAndFiles(IQCFolderPath)) return;
            }

            // 检验MES测试数据文件为csv后缀
            if (!string.IsNullOrEmpty(MESDataFolderPath))
            {
                if (!CheckMESDataFilePattern(MESDataFolderPath)) return;
            }

            // IQC
            if (!string.IsNullOrEmpty(IQCFolderPath))
            {
                ProcessIQCComparison(IQCFolderPath, ResultPath, ResultOutputPath);
            }

            // IPQC
            if (!string.IsNullOrEmpty(IPQCDataFolderPath))
            {
                ProcessIPQCComparison(IPQCDataFolderPath, ResultPath, ResultOutputPath);
            }

            // APMT
            if (!string.IsNullOrEmpty(APMTDataFolderPath))
            {
                ProcessAPMTComparison(APMTDataFolderPath, ResultPath, ResultOutputPath);
            }

            // PCD
            if (!string.IsNullOrEmpty(PCDDataFolderPath))
            {
                ProcessPCDComparison(PCDDataFolderPath, ResultPath, ResultOutputPath);
            }

            // MES
            if (!string.IsNullOrEmpty(MESDataFolderPath))
            {
                ProcessMESComparison(MESDataFolderPath, ResultPath, ResultOutputPath);
            }

            // 所有对比完成，提示执行完毕
            MessageBox.Show("对比结束！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #region MES对比
        private void ProcessMESComparison(string MESDataFolderPath, string ResultPath, string ResultOutputPath)
        {
            // 1. 获取所有测试数据文件（csv）和标准文件（xlsx）
            string[] allFiles = Directory.GetFiles(MESDataFolderPath).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();
            var testFiles = allFiles.Where(f => f.EndsWith(".csv", StringComparison.OrdinalIgnoreCase)).ToArray();
            if (testFiles.Length == 0)
            {
                MessageBox.Show("未找到MES测试文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var specFiles = allFiles.Where(f => f.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase)).ToArray();
            if (specFiles.Length == 0)
            {
                MessageBox.Show("未找到MES标准文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 2. 获取Result模板文件
            string[] resultTemplates = Directory.GetFiles(ResultPath);
            string[] MESResultFile = resultTemplates.Where(item => item.Contains("MES")).ToArray();
            if (MESResultFile.Length == 0)
            {
                MessageBox.Show("未找到MES模板文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
            string MESResultFilePath = MESResultFile[0];

            // 3. 遍历所有测试数据文件
            foreach (var testFile in testFiles)
            {
                // 3.1 从文件名提取机种号（如X2943）
                string testFileName = Path.GetFileName(testFile);
                string model = "";
                var match = System.Text.RegularExpressions.Regex.Match(testFileName, @"(X\d{4,})");
                if (match.Success)
                {
                    // 成功匹配到机种号
                    model = match.Groups[1].Value;
                }
                else
                {
                    MessageBox.Show($"测试文件名未找到机种号: {testFileName}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 3.2 根据机种号匹配对应的标准文件
                var specFile = specFiles.FirstOrDefault(f => Path.GetFileName(f).Contains(model));
                if (specFile == null)
                {
                    MessageBox.Show($"未找到机种 {model} 的标准文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 3.3 读取标准文件的第一个工作表为数据表
                Workbook specWb = new Workbook(specFile);
                Worksheet specSheet = specWb.Worksheets[0];

                ExportTableOptions options = new ExportTableOptions
                {
                    ExportColumnName = true,
                    ExportAsString = true
                };

                DataTable specDt = specSheet.Cells.ExportDataTable(0, 0, specSheet.Cells.MaxDataRow + 1, specSheet.Cells.MaxDataColumn + 1, options);

                // 3.4 读取CSV测试数据为DataTable
                Workbook testFileWb = new Workbook(testFile);
                Worksheet testFileSheet = testFileWb.Worksheets[0];
                DataTable testDt = testFileSheet.Cells.ExportDataTable(0, 0, testFileSheet.Cells.MaxDataRow + 1, testFileSheet.Cells.MaxDataColumn + 1, options);

                // 4. 从标准文件中提取BIS工序名称、时间管控、测试次数信息到字典
                var specBISDict = new Dictionary<string, (string timeControl, string testCount)>();
                foreach (DataRow row in specDt.Rows)
                {
                    string bisName = row["BIS工序名称"]?.ToString();
                    string timeControl = row["时间管控"]?.ToString();
                    string testCount = row["测试次数"]?.ToString();

                    if (!string.IsNullOrEmpty(bisName))
                        specBISDict[bisName] = (timeControl, testCount);
                }

                // 5. 定义关键列名常量
                var sfcNoCol = "SFC_NO";
                var orderNoCol = "ORDER_NO";
                var lsiteLtimeCol = "LSITE_LTIME";
                var operationNoCol = "OPERATION_NO";
                var sStimeGapCol = "S_STIME_GAP";
                var testTotalCol = "TEST_TOTAL";

                // 6. 按SFC_NO分组处理测试数据
                var sfcGroups = testDt.AsEnumerable().GroupBy(r => r[sfcNoCol]?.ToString());

                // 7. 获取或创建"illegel Retest"工作表
                Workbook resultWb = new Workbook(MESResultFilePath);
                Worksheet illegelSheet = resultWb.Worksheets["illegel Retest"] ?? resultWb.Worksheets.Add("illegel Retest");
                int illegelRow = illegelSheet.Cells.MaxDataRow + 1;
                if (illegelRow == 0)
                {
                    illegelSheet.Cells[0, 0].Value = "SFC_NO";
                    illegelSheet.Cells[0, 1].Value = "不合格类型";
                    illegelRow = 1;
                }

                // 8. 对每个SFC组进行验证检查
                foreach (var group in sfcGroups)
                {
                    try
                    {
                        // 按ORDER_NO排序
                        var rows = group.OrderBy(r => int.Parse(r[orderNoCol].ToString())).ToList();
                        string sfcNo = group.Key;

                        // 检查1: ORDER_NO是否从小到大排列
                        bool orderOk = true;
                        for (int i = 1; i < rows.Count; i++)
                        {
                            int prev = int.TryParse(rows[i - 1][orderNoCol]?.ToString(), out int p) ? p : int.MinValue;
                            int curr = int.TryParse(rows[i][orderNoCol]?.ToString(), out int c) ? c : int.MinValue;
                            if (curr < prev)
                            {
                                orderOk = false;
                                break;
                            }
                        }
                        if (!orderOk)
                        {
                            illegelSheet.Cells[illegelRow, 0].Value = sfcNo;
                            illegelSheet.Cells[illegelRow, 1].Value = "ORDER_NO未从小到大排列";
                            illegelRow++;
                        }

                        // 检查2: LSITE_LTIME是否从小到大排列（时间顺序）
                        bool timeOk = true;
                        for (int i = 1; i < rows.Count; i++)
                        {
                            DateTime prev = DateTime.TryParse(rows[i - 1][lsiteLtimeCol]?.ToString(), out DateTime p) ? p : DateTime.MinValue;
                            DateTime curr = DateTime.TryParse(rows[i][lsiteLtimeCol]?.ToString(), out DateTime c) ? c : DateTime.MinValue;
                            if (curr < prev)
                            {
                                timeOk = false;
                                break;
                            }
                        }
                        if (!timeOk)
                        {
                            illegelSheet.Cells[illegelRow, 0].Value = sfcNo;
                            illegelSheet.Cells[illegelRow, 1].Value = "LSITE_LTIME未从小到大排列";
                            illegelRow++;
                        }

                        // 检查3: OPERATION_NO与spec文件BIS工序名称是否一致
                        foreach (var row in rows)
                        {
                            string opNo = row[operationNoCol]?.ToString();
                            if (!specBISDict.ContainsKey(opNo))
                            {
                                illegelSheet.Cells[illegelRow, 0].Value = sfcNo;
                                illegelSheet.Cells[illegelRow, 1].Value = "OPERATION_NO列与spec文件BIS工序名称不一致";
                                illegelRow++;
                                break;
                            }
                        }

                        // 检查4: S_STIME_GAP值是否大于spec中对应BIS工序名称的"时间管控"列值
                        foreach (var row in rows)
                        {
                            string opNo = row[operationNoCol]?.ToString();
                            if (!specBISDict.ContainsKey(opNo)) continue;
                            double a1 = double.TryParse(row[sStimeGapCol]?.ToString(), out double v1) ? v1 : double.MinValue;
                            double b1 = double.TryParse(specBISDict[opNo].timeControl, out double v2) ? v2 : double.MinValue;
                            // 将小时转换为分钟
                            double timeControlMinute = b1 * 60; 

                            if (a1 > timeControlMinute)
                            {
                                illegelSheet.Cells[illegelRow, 0].Value = sfcNo;
                                illegelSheet.Cells[illegelRow, 1].Value = $"S_STIME_GAP值{a1}大于spec中 {opNo} “时间管控”列的值 {timeControlMinute}";
                                illegelRow++;
                            }
                        }

                        // 检查5: TEST_TOTAL值是否大于spec中对应BIS工序名称的"测试次数"列值
                        foreach (var row in rows)
                        {
                            string opNo = row[operationNoCol]?.ToString();
                            if (!specBISDict.ContainsKey(opNo)) continue;

                            // 获取TEST_TOTAL值
                            double a2 = double.TryParse(row[testTotalCol]?.ToString(), out double v2) ? v2 : double.MaxValue;

                            // 获取spec中对应的测试次数值
                            string testCountStr = specBISDict[opNo].testCount;

                            // 如果测试次数不是有效数字(如"/"或空字符串)，则跳过校验
                            if (!double.TryParse(testCountStr, out double b2)) continue;

                            // 执行校验
                            if (a2 > b2)
                            {
                                illegelSheet.Cells[illegelRow, 0].Value = sfcNo;
                                illegelSheet.Cells[illegelRow, 1].Value = $"TEST_TOTAL值大于spec中对应BIS工序名称的“测试次数”列值";
                                illegelRow++;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"处理文件 {group.Key} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }

                // 结果保存
                string dateStr = DateTime.Now.ToString("yyyyMMdd");
                string searchPattern = $"MES_{model}_{dateStr}*.xlsx";
                string[] existFiles = Directory.GetFiles(ResultOutputPath, searchPattern);
                int serial = existFiles.Length + 1;
                string serialStr = serial.ToString("D3");
                string resultFileName = $"MES_{model}_{dateStr}{serialStr}.xlsx";
                string resultFilePath = Path.Combine(ResultOutputPath, resultFileName);
                resultWb.Save(resultFilePath);
            }
        }
        #endregion

        #region PCD对比
        private void ProcessPCDComparison(string PCDDataFolderPath, string ResultPath, string ResultOutputPath)
        {
            try
            {
                // 获取Result模板文件
                string[] resultDirectories = Directory.GetFiles(ResultPath);
                string[] PCDResultFile = resultDirectories.Where(item => item.Contains("PCD")).ToArray();
                if (PCDResultFile.Length == 0)
                {
                    MessageBox.Show("未找到PCD模板文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                string PCDResultFilePath = PCDResultFile[0];

                // 获取PCD根目录下的所有文件（用于查找标准文件）
                string[] rootFiles = Directory.GetFiles(PCDDataFolderPath).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();

                // 获取所有机种文件夹
                string[] modelDirectories = Directory.GetDirectories(PCDDataFolderPath);
                if (modelDirectories.Length == 0)
                {
                    MessageBox.Show("PCD文件夹下未找到机种数据文件夹！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 遍历每个机种文件夹
                foreach (var modelDir in modelDirectories)
                {
                    // 从文件夹名称中提取机种号
                    string dirName = Path.GetFileName(modelDir);
                    string modelName = "";

                    // 使用更精确的正则表达式匹配完整的机种标识符
                    // 匹配格式：以X开头，后跟数字，再跟可选的字母/数字，直到"数据"之前
                    var match = System.Text.RegularExpressions.Regex.Match(dirName, @"(X\d+[a-zA-Z0-9]*?)数据");
                    if (match.Success)
                    {
                        modelName = match.Groups[1].Value;
                    }
                    else
                    {
                        MessageBox.Show($"无法从文件夹名 {dirName} 中提取机种号！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        continue; // 跳过此机种，处理下一个
                    }

                    // 查找对应的标准文件
                    string specFile = rootFiles.FirstOrDefault(f =>
                        Path.GetFileName(f).StartsWith("NVT IPQC Spec_") &&
                        Path.GetFileName(f).Contains(modelName));

                    if (specFile == null)
                    {
                        MessageBox.Show($"未找到机种 {modelName} 的标准文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        continue; // 跳过此机种，处理下一个
                    }

                    // 处理当前机种的数据
                    ProcessSingleModelPCD(modelDir, specFile, modelName, PCDResultFilePath, ResultOutputPath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"PCD数据比对过程出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
        }

        // 处理单个机种的PCD数据
        private void ProcessSingleModelPCD(string modelDir, string specFile, string modelName, string PCDResultFilePath, string ResultOutputPath)
        {
            try
            {
                ExportTableOptions options = new ExportTableOptions
                {
                    ExportColumnName = true,
                    ExportAsString = true
                };

                // 读取标准文件
                Workbook specWb = new Workbook(specFile);
                Worksheet specWs = specWb.Worksheets[0];
                DataTable dt = specWs.Cells.ExportDataTable(0, 0, specWs.Cells.MaxDataRow + 1, specWs.Cells.MaxDataColumn + 1, options);

                // 获取当前机种目录下的所有PCD文件
                string[] PDCFiles = Directory.GetFiles(modelDir).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();

                // 为当前机种创建一个结果模板
                Workbook resultWb = new Workbook(PCDResultFilePath);

                // 结果收集
                var resultList = new List<(string projectCode, string monitorType, string processMsg, string testItemMsg, string fileName)>();
                var sampleCheckList = new List<(string projectCode, string monitorType, string item, string detail, string fileName)>();
                var oosList = new List<(string projectCode, string monitorType, string line, string testTime, string procName, string item, string value)>();

                // 处理每个PCD文件
                foreach (var file in PDCFiles)
                {
                    string ext = Path.GetExtension(file).ToLower();
                    if (ext != ".xls" && ext != ".xlsx") continue;

                    try
                    {
                        // 从文件名提取BOP工序名称（去掉扩展名）
                        string fileName = Path.GetFileNameWithoutExtension(file);
                        string bopProcessName = fileName; // 整个文件名就是BOP工序名称

                        // 解析标准数据 - 使用从文件名提取的BOP工序名称
                        var specDict = new Dictionary<string, (string lsl, string usl, string freq, int sampleSize)>();
                        try
                        {
                            // 筛选指定BOP工序名称的行，获取BOP参数名称、CQP LSL、CQP USL
                            var processRows = dt.AsEnumerable().Where(row => row["BOP工序名称"]?.ToString() == bopProcessName);

                            foreach (var row in processRows)
                            {
                                string paramName = row["BOP参数名称"]?.ToString();
                                string lsl = row["CQP LSL"]?.ToString();
                                string usl = row["CQP USL"]?.ToString();
                                string freq = row.Table.Columns.Contains("Vendor Frequency") ? row["Vendor Frequency"]?.ToString() : "";
                                int sampleSize = 0;
                                if (row.Table.Columns.Contains("Vendor Sample Size"))
                                    int.TryParse(row["Vendor Sample Size"]?.ToString(), out sampleSize);
                                if (!string.IsNullOrEmpty(paramName))
                                    specDict[paramName] = (lsl, usl, freq, sampleSize);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"解析标准数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        Workbook wb = new Workbook(file);
                        Worksheet ws = wb.Worksheets[0]; 

                        // 1. 检查项目和上下限
                        CheckPCDItemAndLimit(ws, specDict, modelName, file, resultList);

                        // 2. Sample Size 检测
                        CheckPCDSampleSize(ws, specDict, modelName, file, sampleCheckList);

                        // 3. OOS 检测
                        CheckPCDOOS(ws, specDict, modelName, file, oosList);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"处理文件 {file} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }

                // 写入各sheet
                try
                {
                    WritePCDItemAndLimitSheet(resultWb, resultList);
                    WritePCDSampleSizeSheet(resultWb, sampleCheckList);
                    WritePCDOOSSheet(resultWb, oosList);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"写入结果数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 保存文件
                // 获取当前日期
                string dateStr = DateTime.Now.ToString("yyyyMMdd");

                // 查找ResultOutputPath下已有的同类文件，确定流水号
                string searchPattern = $"PCD_{modelName}_{dateStr}*.xlsx";
                string[] existFiles = Directory.GetFiles(ResultOutputPath, searchPattern);
                int serial = existFiles.Length + 1;
                string serialStr = serial.ToString("D3");

                // 拼接最终文件名
                string resultFileName = $"PCD_{modelName}_{dateStr}{serialStr}.xlsx";
                string resultFilePath = Path.Combine(ResultOutputPath, resultFileName);

                // 保存
                resultWb.Save(resultFilePath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理机种 {modelName} 的PCD数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 检查项目和上下限
        private void CheckPCDItemAndLimit(Worksheet ws,Dictionary<string, (string lsl, string usl, string freq, int sampleSize)> specDict,string modelName,string file, List<(string, string, string, string, string)> resultList)
        {
            // 定义数据开始的列索引
            int startCol = 8;

            // 获取工作表的最大列和行索引
            int maxCol = ws.Cells.MaxDataColumn;
            int maxRow = ws.Cells.MaxDataRow;

            // 遍历每一列，提取项目名称和上下限值
            var dataDict = new Dictionary<string, (string lsl, string usl)>();
            for (int col = startCol; col <= maxCol; col++)
            {
                // 读取项目名称（第0行）
                string itemName = ws.Cells[0, col]?.StringValue?.Trim() ?? "";

                // 读取上限值（第1行，如果存在）
                string usl = maxRow >= 1 ? ws.Cells[1, col]?.StringValue?.Trim() ?? "" : "";

                // 读取下限值（第2行，如果存在）
                string lsl = maxRow >= 2 ? ws.Cells[2, col]?.StringValue?.Trim() ?? "" : "";

                // 如果项目名称不为空，则添加到字典中
                if (!string.IsNullOrEmpty(itemName))
                    dataDict[itemName] = (lsl, usl);
            }

            // 比较specDict和所有文件项目名称全集
            var notFoundInAllFiles = specDict.Keys.Where(k => !dataDict.ContainsKey(k)).ToList();

            // 检查每个项目的上下限是否与规范一致
            var limitMismatch = new List<string>();
            foreach (var kv in dataDict)
            {
                string item = kv.Key;
                var (lsl, usl) = kv.Value;
                if (specDict.ContainsKey(item))
                {
                    var (specLsl, specUsl, freq, sampleSize) = specDict[item];

                    bool lslEqual, uslEqual;

                    // 下限比较
                    if (double.TryParse(lsl, out double lslVal) && double.TryParse(specLsl, out double specLslVal))
                        lslEqual = Math.Abs(lslVal - specLslVal) < 0.0001;
                    else
                        lslEqual = lsl == specLsl;

                    // 上限比较
                    if (double.TryParse(usl, out double uslVal) && double.TryParse(specUsl, out double specUslVal))
                        uslEqual = Math.Abs(uslVal - specUslVal) < 0.0001;
                    else
                        uslEqual = usl == specUsl;

                    if (!lslEqual || !uslEqual)
                    {
                        limitMismatch.Add($"{item}（spec {specLsl}/{specUsl}, file {lsl}/{usl}）");
                    }
                }
            }

            // 如果有项目在规范中不存在，则添加到结果列表
            if (notFoundInAllFiles.Count > 0)
                resultList.Add((modelName, "PCD", "BOP参数名称文件项目缺失", string.Join("，", notFoundInAllFiles), Path.GetFileName(file)));

            // 如果有项目上下限不一致，则添加到结果列表
            if (limitMismatch.Count > 0)
                resultList.Add((modelName, "PCD", "BOP参数名称上下限错误", string.Join("，", limitMismatch), Path.GetFileName(file)));
        }

        // Sample Size 检测
        private void CheckPCDSampleSize(Worksheet ws,Dictionary<string, (string lsl, string usl, string freq, int sampleSize)> specDict,string modelName,string file,List<(string, string, string, string, string)> sampleCheckList)
        {
            // 定义数据开始的列索引
            int startCol = 8;

            // 获取工作表的最大列和行索引
            int maxCol = ws.Cells.MaxDataColumn;
            int maxRow = ws.Cells.MaxDataRow;

            // 遍历每一列，检查各项目的样本数量
            for (int col = startCol; col <= maxCol; col++)
            {
                // 读取项目名称
                string itemName = ws.Cells[0, col].StringValue.Trim();
                if (string.IsNullOrEmpty(itemName) || !specDict.ContainsKey(itemName)) continue;

                // 获取项目的规范信息
                var (lsl, usl, freq, sampleSize) = specDict[itemName];
                if (string.IsNullOrEmpty(freq)) continue;

                // 从freq中解析出期望的小时间隔
                int expectedHourInterval = 1; // 默认值
                if (!string.IsNullOrEmpty(freq))
                {
                    // 假设freq格式为"1/1h"，提取第二个数字
                    var parts = freq.Split('/');
                    if (parts.Length == 2 && parts[1].EndsWith("h"))
                    {
                        string hourPart = parts[1].TrimEnd('h');
                        if (int.TryParse(hourPart, out int interval) && interval > 0)
                        {
                            expectedHourInterval = interval;
                        }
                    }
                }

                // 用于记录每个"日期+小时"的样本数量
                var hourCount = new Dictionary<string, int>();
                // 用于收集所有时间点
                var timeList = new List<DateTime>();

                // 遍历数据行，收集时间信息
                for (int row = 3; row <= maxRow; row++)
                {
                    string timeStr = ws.Cells[row, 5].StringValue.Trim();
                    if (DateTime.TryParse(timeStr, out DateTime time))
                    {
                        string dayHour = time.ToString("yyyy-MM-dd HH");
                        if (!hourCount.ContainsKey(dayHour)) hourCount[dayHour] = 0;
                        hourCount[dayHour]++;
                        timeList.Add(time);
                    }
                }

                // 检查每个小时的样本数是否满足规范要求
                foreach (var kv in hourCount)
                {
                    if (kv.Value == sampleSize)
                    {
                        sampleCheckList.Add((
                            modelName,
                            "PCD",
                            itemName,
                            $"{itemName}(SPEC {freq} 的样品数为 {sampleSize}/File by({kv.Key})计算的数量为{kv.Value})",
                            Path.GetFileName(file)
                        ));
                    }
                }

                // 检查每天内相邻小时之间的间隔是否正确
                var dayGroups = timeList.GroupBy(t => t.Date);
                foreach (var dayGroup in dayGroups)
                {
                    // 获取当天所有不同的小时，并排序
                    var hours = dayGroup.Select(t => t.Hour).Distinct().OrderBy(h => h).ToList();

                    // 检查相邻小时之间是否有间隔（应该连续）
                    for (int i = 1; i < hours.Count; i++)
                    {
                        if (hours[i] - hours[i - 1] != expectedHourInterval)
                        {
                            string day = dayGroup.Key.ToString("yyyy-MM-dd");
                            int diff = hours[i] - hours[i - 1];
                            sampleCheckList.Add((
                                modelName,
                                "PCD",
                                itemName,
                                $"{itemName}(SPEC {freq}/File {day}({hours[i]})-{day}({hours[i - 1]})={diff},间隔不等于{expectedHourInterval}h)",
                                Path.GetFileName(file)
                            ));
                            break;
                        }
                    }
                }

                // 检查每天是否有缺失的小时
                var dayHourGroups = timeList.GroupBy(t => t.Date);
                foreach (var dayGroup in dayHourGroups)
                {
                    // 获取当天所有不同的小时
                    var hours = dayGroup.Select(t => t.Hour).Distinct().OrderBy(h => h).ToList();

                    // 找出0-23小时中缺失的小时
                    var missingHours = Enumerable.Range(0, 24).Except(hours).ToList();

                    if (missingHours.Count > 0)
                    {
                        string day = dayGroup.Key.ToString("yyyy-MM-dd");
                        sampleCheckList.Add((
                            modelName,
                            "PCD",
                            itemName,
                            $"{itemName}(SPEC {freq}/File {day} 缺失{string.Join(",", missingHours)})",
                            Path.GetFileName(file)
                        ));
                    }
                }
            }
        }

        // OOS 检测
        private void CheckPCDOOS(Worksheet ws,Dictionary<string, (string lsl, string usl, string freq, int sampleSize)> specDict,string modelName,string file,List<(string, string, string, string, string, string, string)> oosList)
        {
            // 定义数据开始的列索引
            int startCol = 8;

            // 获取工作表的最大列和行索引
            int maxCol = ws.Cells.MaxDataColumn;
            int maxRow = ws.Cells.MaxDataRow;

            // 遍历每一列，检查各项目的数据是否超出规格
            for (int col = startCol; col <= maxCol; col++)
            {
                string itemName = ws.Cells[0, col].StringValue.Trim();
                if (string.IsNullOrEmpty(itemName)) continue;

                // 读取上限和下限值
                string uslStr = maxRow >= 1 ? ws.Cells[1, col].StringValue.Trim() : "";
                string lslStr = maxRow >= 2 ? ws.Cells[2, col].StringValue.Trim() : "";

                double usl = double.NaN, lsl = double.NaN;
                double.TryParse(uslStr, out usl);
                double.TryParse(lslStr, out lsl);

                // 遍历每一行数据
                for (int row = 3; row <= maxRow; row++)
                {
                    // 读取测量值
                    string valueStr = ws.Cells[row, col].StringValue.Trim();
                    if (string.IsNullOrEmpty(valueStr)) continue;
                    double value;
                    if (!double.TryParse(valueStr, out value)) continue;

                    // 检查是否超出规格
                    bool oos = false;
                    string oosDetail = "";

                    // 检查是否低于下限
                    if (!double.IsNaN(lsl) && value < lsl)
                    {
                        oos = true;
                        oosDetail = $"{itemName} (spec {lslStr}/{uslStr}, file {value})";
                    }

                    // 检查是否高于上限
                    if (!double.IsNaN(usl) && value > usl)
                    {
                        oos = true;
                        oosDetail = $"{itemName} (spec {lslStr}/{uslStr}, file {value})";
                    }

                    // 如果超出规格，记录详细信息
                    if (oos)
                    {
                        string line = ws.Cells[row, 2].StringValue.Trim(); // 线体
                        string testTime = ws.Cells[row, 5].StringValue.Trim(); // 检测时间
                        string procName = ws.Cells[row, 3].StringValue.Trim(); // 工序
                        oosList.Add((
                            modelName,
                            "PCD",
                            line,
                            testTime,
                            procName,
                            itemName,
                            oosDetail
                        ));
                    }
                }
            }
        }

        // Duplication 检验

        // repeat_sn_result 检验

        private void WritePCDItemAndLimitSheet(Workbook resultWb, List<(string projectCode, string monitorType, string processMsg, string testItemMsg, string fileName)> resultList)
        {
            if (resultList.Count == 0) return;

            Worksheet ngSheet = null;
            try
            {
                ngSheet = resultWb.Worksheets["数据中的工序或检测项在Spec中找不到"];
            }
            catch
            {
                ngSheet = resultWb.Worksheets.Add("数据中的工序或检测项在Spec中找不到");
                ngSheet.Cells[0, 0].Value = "Project Code";
                ngSheet.Cells[0, 1].Value = "监控类型";
                ngSheet.Cells[0, 2].Value = "Process not found in Spec";
                ngSheet.Cells[0, 3].Value = "Test Item not found in Spec";
                ngSheet.Cells[0, 4].Value = "filename";
            }
            int rowIndex = ngSheet.Cells.MaxDataRow + 1;
            foreach (var item in resultList)
            {
                ngSheet.Cells[rowIndex, 0].Value = item.projectCode;
                ngSheet.Cells[rowIndex, 1].Value = item.monitorType;
                ngSheet.Cells[rowIndex, 2].Value = item.processMsg;
                ngSheet.Cells[rowIndex, 3].Value = item.testItemMsg;
                ngSheet.Cells[rowIndex, 4].Value = item.fileName;
                rowIndex++;
            }
        }

        private void WritePCDSampleSizeSheet(Workbook resultWb, List<(string projectCode, string monitorType, string item, string detail, string fileName)> sampleCheckList)
        {
            if (sampleCheckList.Count == 0) return;

            Worksheet sampleSheet = null;
            try
            {
                sampleSheet = resultWb.Worksheets["Sample Size 检测"];
            }
            catch
            {
                sampleSheet = resultWb.Worksheets.Add("Sample Size 检测");
                sampleSheet.Cells[0, 0].Value = "Project Code";
                sampleSheet.Cells[0, 1].Value = "监控类型";
                sampleSheet.Cells[0, 2].Value = "检测项目";
                sampleSheet.Cells[0, 3].Value = "检测时间所在的小时";
                sampleSheet.Cells[0, 4].Value = "filename";
            }
            int rowIndex = sampleSheet.Cells.MaxDataRow + 1;
            foreach (var item in sampleCheckList)
            {
                sampleSheet.Cells[rowIndex, 0].Value = item.projectCode;
                sampleSheet.Cells[rowIndex, 1].Value = item.monitorType;
                sampleSheet.Cells[rowIndex, 2].Value = item.item;
                sampleSheet.Cells[rowIndex, 3].Value = item.detail;
                sampleSheet.Cells[rowIndex, 4].Value = item.fileName;
                rowIndex++;
            }
        }

        private void WritePCDOOSSheet(Workbook resultWb, List<(string projectCode, string monitorType, string line, string testTime, string process, string testItem, string testValue)> oosList)
        {
            if (oosList.Count == 0) return;
            Worksheet oosSheet = null;
            try
            {
                oosSheet = resultWb.Worksheets["OOS"];
            }
            catch
            {
                oosSheet = resultWb.Worksheets.Add("OOS");
                oosSheet.Cells[0, 0].Value = "Project Code";
                oosSheet.Cells[0, 1].Value = "监控类型";
                oosSheet.Cells[0, 2].Value = "线体";
                oosSheet.Cells[0, 3].Value = "检测时间";
                oosSheet.Cells[0, 4].Value = "工序";
                oosSheet.Cells[0, 5].Value = "检测项目";
                oosSheet.Cells[0, 6].Value = "测试值";
            }
            int rowIndex = oosSheet.Cells.MaxDataRow + 1;
            foreach (var item in oosList)
            {
                oosSheet.Cells[rowIndex, 0].Value = item.projectCode;
                oosSheet.Cells[rowIndex, 1].Value = item.monitorType;
                oosSheet.Cells[rowIndex, 2].Value = item.line;
                oosSheet.Cells[rowIndex, 3].Value = item.testTime;
                oosSheet.Cells[rowIndex, 4].Value = item.process;
                oosSheet.Cells[rowIndex, 5].Value = item.testItem;
                oosSheet.Cells[rowIndex, 6].Value = item.testValue;
                rowIndex++;
            }
        }
        #endregion

        #region APMT对比
        private void ProcessAPMTComparison(string APMTDataFolderPath, string ResultPath, string ResultOutputPath)
        {
            try
            {
                // 获取APMT待测试文件夹和标准文件
                string[] APMTDirectories = Directory.GetDirectories(APMTDataFolderPath);
                string[] SpecFiles = Directory.GetFiles(APMTDataFolderPath);

                // 检查必要的文件和目录是否存在
                if (APMTDirectories.Length == 0)
                {
                    MessageBox.Show("未找到APMT测试文件夹！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (SpecFiles.Length == 0)
                {
                    MessageBox.Show("未找到标准文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取Result模板文件
                string[] resultDirectories = Directory.GetFiles(ResultPath);
                string[] APMTResultFile = resultDirectories.Where(item => item.Contains("APMT")).ToArray();
                if (APMTResultFile.Length == 0)
                {
                    MessageBox.Show("未找到APMT模板文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                string APMTResultFilePath = APMTResultFile[0];

                ExportTableOptions options = new ExportTableOptions
                {
                    ExportColumnName = true,
                    ExportAsString = true
                };

                foreach (var dir in APMTDirectories)
                {
                    string folderName = Path.GetFileName(dir); // 例如 "X2821Post APMT点检数据"
                                                               // 提取关键字（如 X2821Post），假设它总是在文件夹名的开头
                    string key = folderName.Split(' ')[0]; // 取第一个空格前的部分

                    // 用key去匹配标准文件名
                    var matchedSpecFile = SpecFiles.FirstOrDefault(f =>
                    {
                        string specFileName = Path.GetFileName(f);
                        return specFileName.Contains(key);
                    });

                    if (matchedSpecFile == null)
                    {
                        MessageBox.Show($"未找到与文件夹 {folderName} 匹配的标准文件！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    // 从标准文件中获取机种名称
                    string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(matchedSpecFile);
                    string project = fileNameWithoutExtension.Split('_')[1];

                    // 获取标准文件
                    Workbook SpecTableWorkbook = new Workbook(matchedSpecFile);
                    Worksheet SpecWorksheet = SpecTableWorkbook.Worksheets[0];
                    DataTable SpecDatatable = SpecWorksheet.Cells.ExportDataTable(0, 0, SpecWorksheet.Cells.MaxDataRow + 1, SpecWorksheet.Cells.MaxDataColumn + 1, options);
                    // 获取标准文件D1列
                    string postColName = SpecWorksheet.Cells["D1"].Value?.ToString();

                    // 获取模板文件
                    Workbook APMTResultTableWorkbook = new Workbook(APMTResultFilePath);

                    List<(string file, DateTime maxTime)> fileMaxTimes = new List<(string, DateTime)>();

                    // 遍历当前文件夹里的所有测试文件,并排除.DS_Store文件
                    string[] files = Directory.GetFiles(dir).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();

                    if (files.Length == 0)
                    {
                        MessageBox.Show($"文件夹 {folderName} 中未找到测试文件！", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    foreach (var file in files)
                    {
                        try
                        {
                            // 获取当前测试文件
                            Workbook APMTTableWorkbook = new Workbook(file);
                            Worksheet worksheet = APMTTableWorkbook.Worksheets[0];
                            // 时间列
                            string timeColName = worksheet.Cells["B1"].Value?.ToString();
                            // 转化成DataTable
                            DataTable TestDatatable = worksheet.Cells.ExportDataTable(0, 0, worksheet.Cells.MaxDataRow + 1, worksheet.Cells.MaxDataColumn + 1, options);

                            // 1. POS缺失检查
                            CheckMissingPOS(project, file, postColName, SpecDatatable, TestDatatable, APMTResultTableWorkbook);

                            // 2. Item组检查
                            var lastRows = CheckItemGroup(project, file, postColName, SpecDatatable, TestDatatable, APMTResultTableWorkbook);

                            // 3. 上下限检查
                            CheckSpecLimits(project, file, SpecDatatable, TestDatatable, lastRows, APMTResultTableWorkbook);

                            // 4. 时间间隔检查
                            UpdateFileMaxTimes(file, timeColName, TestDatatable, lastRows, fileMaxTimes);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"处理文件 {file} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }

                    // 5. 时间间隔结果输出
                    CheckTimeGap(project, fileMaxTimes, SpecDatatable, APMTResultTableWorkbook, SpecWorksheet);

                    // 获取当前日期
                    string dateStr = DateTime.Now.ToString("yyyyMMdd");

                    // 查找ResultOutputPath下已有的同类文件，确定流水号
                    string searchPattern = $"APMT_{project}_{dateStr}*.xlsx";
                    string[] existFiles = Directory.GetFiles(ResultOutputPath, searchPattern);
                    int serial = existFiles.Length + 1;
                    string serialStr = serial.ToString("D3");

                    // 拼接最终文件名
                    string resultFileName = $"APMT_{project}_{dateStr}{serialStr}.xlsx";
                    string resultFilePath = Path.Combine(ResultOutputPath, resultFileName);

                    // 保存
                    APMTResultTableWorkbook.Save(resultFilePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理APMT比对时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
        }

        // POS缺失检查
        private void CheckMissingPOS(string project, string file, string postColName, DataTable SpecDatatable, DataTable TestDatatable, Workbook resultWorkbook)
        {
            // 从规格数据表中读取ST10_POS列的值并存储到specValues列表中
            List<string> specValues = new List<string>();
            for (int i = 0; i < SpecDatatable.Rows.Count; i++)
            {
                var val = SpecDatatable.Rows[i][postColName];
                if (val != DBNull.Value && !string.IsNullOrWhiteSpace(val.ToString()))
                {
                    specValues.Add(val.ToString());
                }
            }

            // 检查测试数据表中是否包含指定的列，如果没有则直接返回
            if (!TestDatatable.Columns.Contains(postColName)) return;

            // 从测试数据表中读取D1列的值并存储到testValues列表中
            int testColIndex = TestDatatable.Columns.IndexOf(postColName);
            List<string> testValues = new List<string>();
            for (int i = 3; i < TestDatatable.Rows.Count; i++)
            {
                var val = TestDatatable.Rows[i][testColIndex];
                if (val != DBNull.Value && !string.IsNullOrWhiteSpace(val.ToString()))
                {
                    testValues.Add(val.ToString());
                }
            }

            // 遍历规格表中的所有POS值，检查是否存在于测试表中
            List<string> missing = new List<string>();
            foreach (var v in specValues)
            {
                if (!testValues.Contains(v))
                {
                    missing.Add(v);
                }
            }

            if (missing.Count > 0)
            {
                Worksheet ngSheet = null;
                try
                {
                    ngSheet = resultWorkbook.Worksheets["NG POS check"];
                }
                catch
                {
                    ngSheet = resultWorkbook.Worksheets.Add("NG POS check");
                    ngSheet.Cells[0, 0].Value = "product_name";
                    ngSheet.Cells[0, 1].Value = "NGpoisitions";
                    ngSheet.Cells[0, 2].Value = "filelist";
                }
                int rowIndex = ngSheet.Cells.MaxDataRow + 1;
                ngSheet.Cells[rowIndex, 0].Value = project;
                ngSheet.Cells[rowIndex, 1].Value = "[" + string.Join(", ", missing) + "]";
                ngSheet.Cells[rowIndex, 2].Value = file;
            }
        }

        // Item组检查
        private List<DataRow> CheckItemGroup(string project, string file, string postColName, DataTable SpecDatatable, DataTable TestDatatable, Workbook resultWorkbook)
        {
            // 存储最后匹配的行
            List<DataRow> lastRows = new List<DataRow>();

            // 从规格数据表中读取POS列的值并存储到specValues列表中
            List<string> specTestItems = new List<string>();
            for (int i = 0; i < SpecDatatable.Rows.Count; i++)
            {
                var val = SpecDatatable.Rows[i]["test_item"];
                if (val != DBNull.Value && !string.IsNullOrWhiteSpace(val.ToString()))
                {
                    specTestItems.Add(val.ToString());
                }
            }

            // 从规格数据表中读取POS列的值并存储到specValues列表中
            List<string> specValues = new List<string>();
            for (int i = 0; i < SpecDatatable.Rows.Count; i++)
            {
                var val = SpecDatatable.Rows[i][postColName];
                if (val != DBNull.Value && !string.IsNullOrWhiteSpace(val.ToString()))
                {
                    specValues.Add(val.ToString());
                }
            }

            // 检查测试数据表是否包含ST10_POS列，如果没有则返回空列表
            if (!TestDatatable.Columns.Contains(postColName)) return new List<DataRow>();

            // 从测试数据表中读取POS列的值并存储到testValues列表中
            int testColIndex = TestDatatable.Columns.IndexOf(postColName);
            List<string> testValues = new List<string>();
            for (int i = 3; i < TestDatatable.Rows.Count; i++)
            {
                var val = TestDatatable.Rows[i][testColIndex];
                if (val != DBNull.Value && !string.IsNullOrWhiteSpace(val.ToString()))
                {
                    testValues.Add(val.ToString());
                }
            }

            // 从测试数据表的末尾向前查找匹配的POS值
            List<int> lastGroupIndexes = new List<int>();
            int foundCount = 0;
            bool orderMatch = false;

            for (int i = testValues.Count - 1; i >= 0; i--)
            {
                // 如果当前POS值在规格表中存在且该索引还未记录过
                if (specValues.Contains(testValues[i]) && !lastGroupIndexes.Contains(i))
                {
                    lastGroupIndexes.Insert(0, i);  // 将当前索引加入到匹配的索引列表的开头
                    foundCount++;  // 增加已找到的匹配数量
                    if (foundCount == specValues.Count)  // 如果找到的匹配数量等于规格表中的POS数量
                    {
                        var group = lastGroupIndexes.Select(idx => testValues[idx]).ToList();  // 获取匹配的POS值
                        if (group.SequenceEqual(specValues)) 
                        {
                            orderMatch = true;// 如果这些匹配的POS值顺序与规格表一致
                        }
                        
                        foreach (var idx in lastGroupIndexes)
                        {
                            lastRows.Add(TestDatatable.Rows[idx + 3]); // 注意：testValues 是从第4行（索引3）开始的
                        }

                        break;
                    }
                }
            }

            // 新逻辑：检查TestDatatable中是否包含SpecDatatable中所有的项
            List<string> missingItems = specTestItems.Where(specTestItem => !TestDatatable.Columns.Contains(specTestItem)).ToList();

            //// 如果没有找到匹配的POS组，记录缺失信息到"Itemcheck"工作表
            //if (!orderMatch && lastGroupIndexes.Count == specValues.Count)
            // 如果有缺失的项，输出到“Itemcheck”页签
            if (missingItems.Count > 0)
            {
                // 缺失，输出到“NG POS check”页签
                Worksheet ngSheet = null;
                try
                {
                    ngSheet = resultWorkbook.Worksheets["Itemcheck"];
                }
                catch
                {
                    ngSheet = resultWorkbook.Worksheets.Add("Itemcheck");
                    ngSheet.Cells[0, 0].Value = "product_name";
                    ngSheet.Cells[0, 1].Value = "NGfilelist";
                }
                int rowIndex = ngSheet.Cells.MaxDataRow + 1;
                ngSheet.Cells[rowIndex, 0].Value = project;
                ngSheet.Cells[rowIndex, 1].Value = file; // 原始文件完整路径
            }

            return lastRows;
        }

        // 上下限检查
        private void CheckSpecLimits(string project, string file, DataTable SpecDatatable, DataTable TestDatatable, List<DataRow> lastRows, Workbook resultWorkbook)
        {
            // test_itemList 存储所有需要校验的 test_item（如 FAI47、FAI49等）
            List<string> test_itemList = new List<string>();

            // 遍历 TestDatatable 的列，找出那些在 SpecDatatable 中存在的 test_item
            foreach (DataColumn col in TestDatatable.Columns)
            {
                // 只校验在SpecDatatable中存在的test_item
                if (SpecDatatable.AsEnumerable().Any(r => r["test_item"].ToString() == col.ColumnName))
                {
                    test_itemList.Add(col.ColumnName);
                }
            }

            // 遍历 lastRows 中的每一行数据进行校验
            foreach (var row in lastRows)
            {
                // 对于每个需要校验的 test_item
                foreach (var test_item in test_itemList)
                {
                    // 获取当前 test_item 对应的值
                    string valueStr = row[test_item]?.ToString();

                    // 如果值为空或者无法转换为数字，跳过当前 test_item
                    if (string.IsNullOrWhiteSpace(valueStr)) continue;
                    if (!double.TryParse(valueStr, out double value)) continue;

                    // 查找对应 test_item 的规格行
                    var specRow = SpecDatatable.AsEnumerable().FirstOrDefault(r => r["test_item"].ToString() == test_item);
                    if (specRow == null) continue;

                    // 获取规格的 LSL（下限）和 USL（上限）值
                    string lslStr = specRow["lsl"]?.ToString();
                    string uslStr = specRow["usl"]?.ToString();

                    // 用于标记是否超出规格
                    bool outOfSpec = false;
                    string detail = "";

                    // 校验下限 LSL
                    if (double.TryParse(lslStr, out double lsl))
                    {
                        if (value < lsl)
                        {
                            outOfSpec = true;
                            detail += $"值{value} < LSL({lsl}) ";
                        }
                    }

                    // 校验上限 USL
                    if (double.TryParse(uslStr, out double usl))
                    {
                        if (value > usl)
                        {
                            outOfSpec = true;
                            detail += $"值{value} > USL({usl}) ";
                        }
                    }

                    // 如果发现值超出规格范围
                    if (outOfSpec)
                    {
                        Worksheet judgeSheet = null;
                        try
                        {
                            judgeSheet = resultWorkbook.Worksheets["Judge check"];
                        }
                        catch
                        {
                            judgeSheet = resultWorkbook.Worksheets.Add("Judge check");
                            judgeSheet.Cells[0, 0].Value = "product_name";
                            judgeSheet.Cells[0, 1].Value = "test_item";
                            judgeSheet.Cells[0, 2].Value = "spec lsl/usl";
                            judgeSheet.Cells[0, 3].Value = "data";
                            judgeSheet.Cells[0, 4].Value = "filelist";
                        }
                        int rowIndex = judgeSheet.Cells.MaxDataRow + 1;
                        judgeSheet.Cells[rowIndex, 0].Value = project;
                        judgeSheet.Cells[rowIndex, 1].Value = test_item;
                        judgeSheet.Cells[rowIndex, 2].Value = $"{lslStr}/{uslStr}";
                        judgeSheet.Cells[rowIndex, 3].Value = valueStr;
                        judgeSheet.Cells[rowIndex, 4].Value = file;
                    }
                }
            }
        }

        // 时间间隔收集
        private void UpdateFileMaxTimes(string file, string timeColName, DataTable TestDatatable, List<DataRow> lastRows, List<(string file, DateTime maxTime)> fileMaxTimes)
        {
            // 获取时间列的索引位置
            int timeColIndex = TestDatatable.Columns.IndexOf(timeColName);

            // 初始化 maxTime 为 DateTime.MinValue，表示当前没有找到任何有效的时间
            DateTime maxTime = DateTime.MinValue;

            // 遍历 lastRows 中的每一行数据
            foreach (var row in lastRows)
            {
                // 获取当前行中时间列的值，并尝试将其转换为 DateTime 类型
                string timeStr = row[timeColIndex]?.ToString();
                if (DateTime.TryParse(timeStr, out DateTime t)) // 如果转换成功
                {
                    // 比较当前时间与已记录的最大时间，如果当前时间更晚，则更新 maxTime
                    if (t > maxTime)
                    {
                        maxTime = t; // 更新最大时间
                    }
                }
            }

            // 如果 maxTime 被更新过（即有有效的时间值），将文件和对应的最大时间加入 fileMaxTimes 列表
            if (maxTime > DateTime.MinValue)
            {
                fileMaxTimes.Add((file, maxTime)); // 添加文件名和最大时间的元组到列表
            }
        }

        // 时间间隔检查
        private void CheckTimeGap(string project, List<(string file, DateTime maxTime)> fileMaxTimes, DataTable SpecDatatable, Workbook resultWorkbook, Worksheet SpecWorksheet)
        {
            string colName = SpecWorksheet.Cells["E1"].Value?.ToString();

            // 按时间顺序排序文件列表
            fileMaxTimes = fileMaxTimes.OrderBy(x => x.maxTime).ToList();

            // 从规格表获取频率参数（单位：天）
            double freqDay = 1; // 默认频率为1天
            if (SpecDatatable.Columns.Contains(colName))
            {
                // 尝试从规格表的第一行获取频率值
                var freqStr = SpecDatatable.Rows[0][colName]?.ToString();
                double.TryParse(freqStr, out freqDay); // 如果解析失败，freqDay保持默认值1
            }

            // 计算理论上的最大时间间隔（秒）
            double theorySeconds = freqDay * 24 * 3600; // 天 * 小时/天 * 秒/小时

            // 遍历文件列表，检查相邻文件间的时间间隔
            for (int i = 1; i < fileMaxTimes.Count; i++)
            {
                var prev = fileMaxTimes[i - 1]; // 前一个文件及其最大时间
                var curr = fileMaxTimes[i];     // 当前文件及其最大时间

                // 计算实际时间间隔（秒）
                double actualSeconds = (curr.maxTime - prev.maxTime).TotalSeconds;

                // 计算实际间隔与理论间隔的差值（秒）
                double diffSeconds = actualSeconds - theorySeconds;

                // 如果实际间隔超过理论间隔，记录这个异常
                if (actualSeconds > theorySeconds)
                {
                    // 获取或创建"Time check"工作表
                    Worksheet timeSheet = null;
                    try
                    {
                        // 尝试获取已存在的"Time check"工作表
                        timeSheet = resultWorkbook.Worksheets["Time check"];
                    }
                    catch
                    {
                        // 如果不存在，则创建新的工作表并添加表头
                        timeSheet = resultWorkbook.Worksheets.Add("Time check");
                        timeSheet.Cells[0, 0].Value = "product_na"; // 产品名称
                        timeSheet.Cells[0, 1].Value = "firtime";    // 前一个时间点
                        timeSheet.Cells[0, 2].Value = "lasttime";   // 当前时间点
                        timeSheet.Cells[0, 3].Value = "time_Gap(s)"; // 时间差值（秒）
                    }

                    // 获取下一个可用行的索引
                    int rowIndex = timeSheet.Cells.MaxDataRow + 1;

                    // 填充数据
                    timeSheet.Cells[rowIndex, 0].Value = project;       // 项目名称
                    timeSheet.Cells[rowIndex, 1].Value = prev.maxTime;  // 前一个文件的最大时间
                    timeSheet.Cells[rowIndex, 2].Value = curr.maxTime;  // 当前文件的最大时间
                    timeSheet.Cells[rowIndex, 3].Value = diffSeconds;   // 超出理论间隔的秒数

                    // 为日期时间列设置格式，使其显示为"年/月/日 时:分:秒"
                    timeSheet.Cells[rowIndex, 1].SetStyle(new Style() { Custom = "yyyy-MM-dd HH:mm:ss" });
                    timeSheet.Cells[rowIndex, 2].SetStyle(new Style() { Custom = "yyyy-MM-dd HH:mm:ss" });
                }
            }
        }
        #endregion

        #region IQC对比
        private void ProcessIQCComparison(string IQCFolderPath, string ResultPath, string ResultOutputPath)
        {
            try
            {
                // 获取标准文件
                string[] files = Directory.GetFiles(IQCFolderPath).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();

                // 检查是否找到了标准文件
                if (files.Length == 0)
                {
                    MessageBox.Show("IQC文件夹下未找到标准文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 获取Result模板文件
                string[] resultDirectories = Directory.GetFiles(ResultPath);
                string[] IQCResultFile = resultDirectories.Where(item => item.Contains("IQC")).ToArray();

                // 检查是否找到了IQC模板文件
                if (IQCResultFile.Length == 0)
                {
                    MessageBox.Show("未找到IQC模板文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                string IQCResultFilePath = IQCResultFile[0];

                // 设置Excel导出选项
                ExportTableOptions options = new ExportTableOptions
                {
                    ExportColumnName = true,
                    ExportAsString = true
                };

                // 加载标准文件的第一个工作簿
                Workbook SpecTableWorkbook = new Workbook(files[0]);

                // 将标准文件中每个工作表的数据保存到字典中
                Dictionary<string, DataTable> SpecSheetDataList = new Dictionary<string, DataTable>();
                foreach (Worksheet worksheet in SpecTableWorkbook.Worksheets)
                {
                    string sheetName = worksheet.Name;
                    DataTable sheetDatatable = worksheet.Cells.ExportDataTable(0, 0, worksheet.Cells.MaxDataRow + 1, worksheet.Cells.MaxDataColumn + 1, options);
                    SpecSheetDataList.Add(sheetName, sheetDatatable);
                }

                // 文件夹下的所有二级测试文件夹
                string[] IQCDirectories = Directory.GetDirectories(IQCFolderPath);

                // 针对每个二级文件夹单独输出
                foreach (var directory in IQCDirectories)
                {
                    string[] IQCFiles = Directory.GetFiles(directory).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();
                    if (IQCFiles.Length == 0) continue; // 如果文件夹内没有文件，则跳过

                    // 使用模板创建一个新的工作簿
                    Workbook IQCResultTableWorkbook = new Workbook(IQCResultFilePath);

                    // 解析文件中的APN信息
                    HashSet<string> apns = ParseAPNs(IQCFiles);

                    // 从文件夹中提取机种名称
                    string dirName = Path.GetFileName(directory);
                    int startIndex = dirName.IndexOf('X');
                    int endIndex = dirName.IndexOf("IQC");
                    string projectCode = null;
                    if (startIndex >= 0 && endIndex > startIndex)
                    {
                        projectCode = dirName.Substring(startIndex, endIndex - startIndex).TrimEnd();
                    }

                    if (string.IsNullOrEmpty(projectCode))
                    {
                        MessageBox.Show($"无法从文件夹名 {dirName} 中提取机种代码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 找到对应的SpecTable
                    DataTable specTable = null;
                    foreach (var sheetName in SpecSheetDataList.Keys)
                    {
                        if (sheetName.Contains(projectCode))
                        {
                            specTable = SpecSheetDataList[sheetName];
                            break;
                        }
                    }

                    if (specTable != null)
                    {
                        // 物料编码检查每个文件夹只调用一次
                        CheckMissingMaterialCodes(projectCode, apns, specTable, IQCResultTableWorkbook);
                    }

                    // 遍历文件夹中的每个IQC文件
                    foreach (var IQCFile in IQCFiles)
                    {
                        // 新增A13单元格检查
                        Workbook IQCTableWorkbook = new Workbook(IQCFile);
                        Worksheet worksheet = IQCTableWorkbook.Worksheets[0];
                        string a13Value = worksheet.Cells[12, 0].StringValue.Trim();
                        if (a13Value != "L.CPK值")
                        {
                            MessageBox.Show($"文件 {IQCFile} 不符合格式要求！", "格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        string fileName = Path.GetFileName(IQCFile);
                        string nameWithoutExtension = fileName.Substring(0, fileName.LastIndexOf('.'));
                        string[] parts = nameWithoutExtension.Split('_');
                        if (parts.Length < 2) continue; // 如果文件名不符合预期格式，跳过

                        string fileProject = parts[0];
                        string apn = parts[1];

                        // 文件夹名称的机种必须和文件里的名称机种保持一致
                        if (projectCode != fileProject) break;

                        // 遍历SpecSheetDataList中的每个工作表
                        foreach (var sheetName in SpecSheetDataList.Keys)
                        {
                            // 仅处理与当前机种名称相关的工作表
                            if (sheetName.Contains(fileProject))
                            {
                                try
                                {
                                    IQCTableWorkbook = new Workbook(IQCFile);
                                    DataTable SpecTable = SpecSheetDataList[sheetName];
                                    worksheet = IQCTableWorkbook.Worksheets[0];
                                    DataTable sheetDatatable = worksheet.Cells.ExportDataTable(0, 0, worksheet.Cells.MaxDataRow + 1, worksheet.Cells.MaxDataColumn + 1, options);

                                    // 依次调用各个检查方法
                                    CheckMissingTestItems(fileProject, apn, SpecTable, sheetDatatable, IQCResultTableWorkbook);
                                    CheckSampleSize(fileProject, apn, fileName, SpecTable, sheetDatatable, IQCResultTableWorkbook);
                                    CheckSpecLimits(fileProject, apn, fileName, SpecTable, sheetDatatable, IQCResultTableWorkbook);
                                    CheckOOS(fileProject, apn, fileName, SpecTable, sheetDatatable, IQCResultTableWorkbook);
                                    CheckUnit(fileProject, apn, fileName, SpecTable, sheetDatatable, IQCResultTableWorkbook);

                                    break; // 跳出工作表循环，处理下一个文件
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"处理文件 {IQCFile} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                    return;
                                }
                            }
                        }
                    }

                    // 获取当前日期
                    string dateStr = DateTime.Now.ToString("yyyyMMdd");

                    // 使用机种代码生成文件名模式
                    string searchPattern = $"IQC_{projectCode}_{dateStr}*.xlsx";

                    // 获取当前目录下所有符合模式的文件
                    string[] existFiles = Directory.GetFiles(ResultOutputPath, searchPattern);

                    // 计算流水号，已有文件数量 + 1
                    int serial = existFiles.Length + 1;
                    string serialStr = serial.ToString("D3");  // 流水号补零为三位数

                    // 构建最终的文件名
                    string resultFileName = $"IQC_{projectCode}_{dateStr}{serialStr}.xlsx";

                    // 拼接完整文件路径
                    string resultFilePath = Path.Combine(ResultOutputPath, resultFileName);

                    // 保存生成的Excel文件
                    IQCResultTableWorkbook.Save(resultFilePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"IQC比对过程出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }
        }

        private void CheckMissingTestItems(string project, string apn, DataTable SpecTable, DataTable sheetDatatable, Workbook IQCResultTableWorkbook)
        {
            // 从SpecTable中选择与给定APN匹配的行
            DataRow[] DropSubRows = SpecTable.Select($"[Material Code]='{apn}'");

            // 从筛选出的行中获取所有 Test Item
            HashSet<string> standardTestItems = new HashSet<string>();
            foreach (DataRow row in DropSubRows)
            {
                if (row["Test Item"] != DBNull.Value)
                {
                    standardTestItems.Add(row["Test Item"].ToString());
                }
            }

            // 获取IQC测试数据中的所有列名（Test Item 是列名）
            HashSet<string> iqcTestItems = new HashSet<string>();
            for (int i = 1; i < sheetDatatable.Columns.Count; i++) 
            {
                iqcTestItems.Add(sheetDatatable.Columns[i].ColumnName);
            }

            // 找出在标准文件中，但不在 IQC 测试数据列名中的 Test Item
            HashSet<string> missingItems = new HashSet<string>();
            foreach (var standardItem in standardTestItems)
            {
                if (!iqcTestItems.Contains(standardItem))
                {
                    missingItems.Add(standardItem);
                }
            }

            // 如果有缺失的项，将其添加到IQCResult模板“Missing Item”页签中
            if (missingItems.Count > 0)
            {
                Worksheet missingItemSheet = IQCResultTableWorkbook.Worksheets["Missing Item"];

                if (missingItemSheet == null)
                {
                    // 如果"Missing Item"页签不存在，创建新的页签
                    missingItemSheet = IQCResultTableWorkbook.Worksheets.Add("Missing Item");
                    missingItemSheet.Cells[0, 0].Value = "Project Code";  // 列标题
                    missingItemSheet.Cells[0, 1].Value = "Material Code";  // 列标题
                    missingItemSheet.Cells[0, 2].Value = "Missing Item";   // 列标题
                }

                // 查找"Missing Item"页签中下一个可用的行
                int rowIndex = missingItemSheet.Cells.MaxDataRow + 1;

                // 将缺失的项添加到该页签
                foreach (var missingItem in missingItems)
                {
                    missingItemSheet.Cells[rowIndex, 0].Value = project;  // 项目代码
                    missingItemSheet.Cells[rowIndex, 1].Value = apn;      // 物料代码
                    missingItemSheet.Cells[rowIndex, 2].Value = missingItem;  // 缺失项
                    rowIndex++;
                }
            }
        }

        private void CheckMissingMaterialCodes(string project, HashSet<string> apns, DataTable SpecTable, Workbook IQCResultTableWorkbook)
        {
            // 获取标准文件中该项目的所有Material Code
            HashSet<string> allMaterialCodes = new HashSet<string>();
            foreach (DataRow row in SpecTable.Rows)
            {
                if (row["Material Code"] != DBNull.Value)
                {
                    string materialCode = row["Material Code"].ToString();
                    allMaterialCodes.Add(materialCode);
                }
            }

            // 找出缺失的Material Code
            List<string> missingMaterialCodes = allMaterialCodes.Where(code => !apns.Contains(code)).ToList();

            if (missingMaterialCodes.Count > 0)
            {
                // 获取“物料编码”页签
                Worksheet materialCodeSheet = IQCResultTableWorkbook.Worksheets["物料编码"];

                // 如果"物料编码"页签不存在，创建新的页签并添加标题行
                if (materialCodeSheet == null)
                {
                    // 如果"物料编码"页签不存在，创建新的页签并添加标题行
                    materialCodeSheet = IQCResultTableWorkbook.Worksheets.Add("物料编码");
                    materialCodeSheet.Cells[0, 0].Value = "Project Code";
                    materialCodeSheet.Cells[0, 1].Value = "Spec Material Codes Count";
                    materialCodeSheet.Cells[0, 2].Value = "Provided Material Codes Count";
                    materialCodeSheet.Cells[0, 3].Value = "Missing Materical Codes in Data Collention";
                    materialCodeSheet.Cells[0, 4].Value = "Missing Materical Codes in Spec";
                }

                // 查找"物料编码"页签中下一个可用的行
                int rowIndex = materialCodeSheet.Cells.MaxDataRow + 1;
                if (rowIndex == 0) rowIndex = 1; // 如果是新创建的页签，从第1行开始（第0行是标题）

                // 添加数据行
                materialCodeSheet.Cells[rowIndex, 0].Value = project;  // 项目代码/机种
                materialCodeSheet.Cells[rowIndex, 1].Value = allMaterialCodes.Count;  // spec表Material Code数量
                materialCodeSheet.Cells[rowIndex, 2].Value = apns.Count;  // 提供的Material Code数量
                materialCodeSheet.Cells[rowIndex, 3].Value = missingMaterialCodes.Count;  // 缺失数量

                // 将缺失的Material Code列表添加到第5列
                string missingCodes = string.Join(", ", missingMaterialCodes);
                materialCodeSheet.Cells[rowIndex, 4].Value = missingCodes;  // 缺失的Material Code列表
            }
        }

        private void CheckSampleSize(string project, string apn, string fileName, DataTable SpecTable, DataTable sheetDatatable, Workbook IQCResultTableWorkbook)
        {
            // 从规格表中筛选出与当前物料代码匹配的行
            DataRow[] DropSubRows = SpecTable.Select($"[Material Code]='{apn}'");

            // 从筛选出的行中获取所有 Test Item
            List<string> standardTestItems = new List<string>();
            foreach (DataRow row in DropSubRows)
            {
                if (row["Test Item"] != DBNull.Value)
                {
                    standardTestItems.Add(row["Test Item"].ToString());
                }
            }

            // 获取IQC测试数据中的所有列名（Test Item 是列名）
            List<string> iqcTestItems = new List<string>();
            foreach (DataColumn column in sheetDatatable.Columns)
            {
                iqcTestItems.Add(column.ColumnName);
            }

            // 如果有匹配的Test Item，检查样本数量是否相等
            List<object[]> sampleSizeIssues = new List<object[]>();

            foreach (var standardTestItem in standardTestItems)
            {
                // 如果IQC测试数据中包含该Test Item
                if (iqcTestItems.Contains(standardTestItem))
                {
                    // 获取标准文件中该Test Item的样本数量
                    int specSampleSize = 0;
                    foreach (DataRow row in DropSubRows)
                    {
                        if (row["Test Item"].ToString() == standardTestItem && row["样本数"] != DBNull.Value)
                        {
                            if (int.TryParse(row["样本数"].ToString(), out int size))
                            {
                                specSampleSize = size;
                                break;
                            }
                        }
                    }

                    // 获取IQC测试数据中该Test Item的实际样本数量（从第14行到最后一行的非空单元格数量）
                    int testAmount = 0;
                    int columnIndex = sheetDatatable.Columns.IndexOf(standardTestItem);
                    if (columnIndex >= 0)
                    {
                        // 从第13行开始计数（DataTable索引从0开始，所以是12）
                        for (int i = 12; i < sheetDatatable.Rows.Count; i++)
                        {
                            if (sheetDatatable.Rows[i][columnIndex] != DBNull.Value &&
                                !string.IsNullOrWhiteSpace(sheetDatatable.Rows[i][columnIndex].ToString()))
                            {
                                testAmount++;
                            }
                        }
                    }

                    // 如果样本数量不相等，记录不合格项
                    if (specSampleSize != testAmount && specSampleSize > 0)
                    {
                        sampleSizeIssues.Add(new object[] {
                            project, apn, fileName, standardTestItem, specSampleSize, testAmount
                        });
                    }
                }
            }

            // 如果有样本数量不合格的项，将其添加到"样本数"页签中
            if (sampleSizeIssues.Count > 0)
            {
                Worksheet sampleSizeSheet = IQCResultTableWorkbook.Worksheets["样本数"];

                if (sampleSizeSheet == null)
                {
                    // 如果"样本数"页签不存在，创建新的页签
                    sampleSizeSheet = IQCResultTableWorkbook.Worksheets.Add("样本数");
                    sampleSizeSheet.Cells[0, 0].Value = "Project Code";     // 机种
                    sampleSizeSheet.Cells[0, 1].Value = "Material Code";    // Material Code
                    sampleSizeSheet.Cells[0, 2].Value = "File/Sheet";       // 待校验文件名称
                    sampleSizeSheet.Cells[0, 3].Value = "Test Item";        // spec中的Test Item
                    sampleSizeSheet.Cells[0, 4].Value = "Spec Sample Size"; // spec中的Test Item的样本数量
                    sampleSizeSheet.Cells[0, 5].Value = "Test Amount";      // 文件中的每列Test Item从14行到最后行计数
                }

                // 查找"样本数"页签中下一个可用的行
                int rowIndex = sampleSizeSheet.Cells.MaxDataRow + 1;

                // 将不合格项添加到该页签
                foreach (var issue in sampleSizeIssues)
                {
                    for (int i = 0; i < issue.Length; i++)
                    {
                        sampleSizeSheet.Cells[rowIndex, i].Value = issue[i];
                    }
                    rowIndex++;
                }
            }
        }

        private void CheckSpecLimits(string project, string apn, string fileName, DataTable SpecTable, DataTable sheetDatatable, Workbook IQCResultTableWorkbook)
        {
            // 从规格表中筛选出与当前物料代码匹配的行
            DataRow[] DropSubRows = SpecTable.Select($"[Material Code]='{apn}'");

            // 从筛选出的行中获取所有 Test Item
            List<string> standardTestItems = new List<string>();
            foreach (DataRow row in DropSubRows)
            {
                if (row["Test Item"] != DBNull.Value)
                {
                    standardTestItems.Add(row["Test Item"].ToString());
                }
            }

            // 获取IQC测试数据中的所有列名（Test Item 是列名）
            List<string> iqcTestItems = new List<string>();
            foreach (DataColumn column in sheetDatatable.Columns)
            {
                iqcTestItems.Add(column.ColumnName);
            }

            // 检查spec的上下限和待校验文件A.USL、B.LSL的上下限是否一致
            List<object[]> specLimitIssues = new List<object[]>();

            foreach (var standardTestItem in standardTestItems)
            {
                // 如果IQC测试数据中包含该Test Item
                if (iqcTestItems.Contains(standardTestItem))
                {
                    // 获取标准文件中该Test Item的上下限
                    string specUpperLimit = string.Empty;
                    string specLowerLimit = string.Empty;
                    bool hasSpecLimits = false;

                    foreach (DataRow row in DropSubRows)
                    {
                        if (row["Test Item"].ToString() == standardTestItem)
                        {
                            // 获取上限
                            string maxValue = row["Max"].ToString();
                            if (maxValue != "NA" && row["Max"] != DBNull.Value &&
                                double.TryParse(maxValue, out double upper))
                            {
                                specUpperLimit = upper.ToString();
                                hasSpecLimits = true;
                            }
                            else if (maxValue == "NA")
                            {
                                specUpperLimit = "NA";
                                hasSpecLimits = true;
                            }

                            // 获取下限
                            string minValue = row["Min"].ToString();
                            if (minValue != "NA" && row["Min"] != DBNull.Value &&
                                double.TryParse(minValue, out double lower))
                            {
                                specLowerLimit = lower.ToString();
                                hasSpecLimits = true;
                            }
                            else if (minValue == "NA")
                            {
                                specLowerLimit = "NA";
                                hasSpecLimits = true;
                            }

                            break;
                        }
                    }

                    // 如果标准文件中有上下限，则检查IQC测试数据中的上下限
                    if (hasSpecLimits)
                    {
                        // 获取IQC测试数据中该Test Item的上下限（第2行和第3行）
                        string dataUpperLimit = string.Empty;
                        string dataLowerLimit = string.Empty;
                        bool hasDataLimits = false;
                        int columnIndex = sheetDatatable.Columns.IndexOf(standardTestItem);

                        if (columnIndex >= 0 && sheetDatatable.Rows.Count > 2)
                        {
                            // 获取上限（第2行，A.USL）
                            string upperValue = sheetDatatable.Rows[0][columnIndex].ToString();
                            if (!string.IsNullOrEmpty(upperValue) && double.TryParse(upperValue, out double upper))
                            {
                                dataUpperLimit = upper.ToString();
                                hasDataLimits = true;
                            }
                            else if (string.IsNullOrEmpty(upperValue))
                            {
                                dataUpperLimit = string.Empty;
                            }

                            // 获取下限（第3行，B.LSL）
                            string lowerValue = sheetDatatable.Rows[1][columnIndex].ToString();
                            if (!string.IsNullOrEmpty(lowerValue) && double.TryParse(lowerValue, out double lower))
                            {
                                dataLowerLimit = lower.ToString();
                                hasDataLimits = true;
                            }
                            else if (string.IsNullOrEmpty(lowerValue))
                            {
                                dataLowerLimit = string.Empty;
                            }
                        }

                        // 如果IQC测试数据中也有上下限，则比较是否一致
                        if (hasDataLimits)
                        {
                            bool upperLimitMatch = IsLimitMatch(specUpperLimit, dataUpperLimit);
                            bool lowerLimitMatch = IsLimitMatch(specLowerLimit, dataLowerLimit);

                            // 如果上限或下限不一致，记录不合格项
                            if (!upperLimitMatch || !lowerLimitMatch)
                            {
                                string upperLimitMessage = upperLimitMatch ?
                                    "" : $"Spec upper Limit is {specUpperLimit} but data upper limit is {dataUpperLimit}";

                                string lowerLimitMessage = lowerLimitMatch ?
                                    "" : $"Spec lower Limit is {specLowerLimit} but data lower limit is {dataLowerLimit}";

                                specLimitIssues.Add(new object[] {
                                                        project,                // Project Code
                                                        apn,                    // Material Code
                                                        fileName,               // File/Sheet
                                                        standardTestItem,       // Test Item
                                                        upperLimitMessage,      // Upper Limit
                                                        lowerLimitMessage       // Lower Limit
                                                    });
                            }
                        }
                    }
                }
            }

            // 如果有上下限不一致的项，将其添加到"Spec"页签中
            if (specLimitIssues.Count > 0)
            {
                Worksheet specSheet = IQCResultTableWorkbook.Worksheets["Spec"];

                if (specSheet == null)
                {
                    // 如果"Spec"页签不存在，创建新的页签
                    specSheet = IQCResultTableWorkbook.Worksheets.Add("Spec");
                    specSheet.Cells[0, 0].Value = "Project Code";     // 机种
                    specSheet.Cells[0, 1].Value = "Material Code";    // APN
                    specSheet.Cells[0, 2].Value = "File/Sheet";       // 待校验文件名称
                    specSheet.Cells[0, 3].Value = "Test Item";        // spec中的Test Item
                    specSheet.Cells[0, 4].Value = "Upper Limit";      // 上限值比较
                    specSheet.Cells[0, 5].Value = "Lower Limit";      // 下限值比较
                }

                // 查找"Spec"页签中下一个可用的行
                int rowIndex = specSheet.Cells.MaxDataRow + 1;

                // 将不合格项添加到该页签
                foreach (var issue in specLimitIssues)
                {
                    for (int i = 0; i < issue.Length; i++)
                    {
                        specSheet.Cells[rowIndex, i].Value = issue[i];
                    }
                    rowIndex++;
                }
            }
        }

        private void CheckOOS(string project, string apn, string fileName, DataTable SpecTable, DataTable sheetDatatable, Workbook IQCResultTableWorkbook)
        {
            // 从规格表中筛选出与当前物料代码匹配的行
            DataRow[] DropSubRows = SpecTable.Select($"[Material Code]='{apn}'");

            // 从筛选出的行中获取所有 Test Item
            List<string> standardTestItems = new List<string>();
            foreach (DataRow row in DropSubRows)
            {
                if (row["Test Item"] != DBNull.Value)
                {
                    standardTestItems.Add(row["Test Item"].ToString());
                }
            }

            // 获取IQC测试数据中的所有列名（Test Item 是列名）
            List<string> iqcTestItems = new List<string>();
            foreach (DataColumn column in sheetDatatable.Columns)
            {
                iqcTestItems.Add(column.ColumnName);
            }

            List<object[]> oosIssues = new List<object[]>();

            foreach (var standardTestItem in standardTestItems)
            {
                if (iqcTestItems.Contains(standardTestItem))
                {
                    // 获取标准文件中的上下限
                    string specUpperStr = "";
                    string specLowerStr = "";
                    foreach (DataRow row in DropSubRows)
                    {
                        if (row["Test Item"].ToString() == standardTestItem)
                        {
                            specUpperStr = row["Max"].ToString();
                            specLowerStr = row["Min"].ToString();
                            break;
                        }
                    }

                    int columnIndex = sheetDatatable.Columns.IndexOf(standardTestItem);
                    if (columnIndex >= 0)
                    {
                        for (int i = 12; i < sheetDatatable.Rows.Count; i++) // 第14行开始
                        {
                            var valueObj = sheetDatatable.Rows[i][columnIndex];
                            string valueStr = valueObj?.ToString();
                            if (string.IsNullOrWhiteSpace(valueStr)) continue;

                            // 只校验能转成数值的情况
                            if (double.TryParse(valueStr, out double sampleValue))
                            {
                                bool oos = false;
                                string detail = "";

                                // 校验上限（如果有上限）
                                if (!string.IsNullOrEmpty(specUpperStr) && specUpperStr != "NA" && double.TryParse(specUpperStr, out double specUpper))
                                {
                                    if (sampleValue > specUpper)
                                    {
                                        oos = true;
                                        detail += $"Value {sampleValue} > Upper {specUpper}. ";
                                    }
                                }
                                // 校验下限（如果有下限）
                                if (!string.IsNullOrEmpty(specLowerStr) && specLowerStr != "NA" && double.TryParse(specLowerStr, out double specLower))
                                {
                                    if (sampleValue < specLower)
                                    {
                                        oos = true;
                                        detail += $"Value {sampleValue} < Lower {specLower}. ";
                                    }
                                }
                                if (oos)
                                {
                                    oosIssues.Add(new object[] {
                                                            project,
                                                            apn,
                                                            fileName,
                                                            standardTestItem,
                                                            valueStr,
                                                            detail.Trim(),
                                                            specUpperStr,
                                                            specLowerStr
                                                        });
                                }
                            }
                            // 不能转成数值的样本值直接跳过，不输出
                        }
                    }
                }
            }

            // 输出到“OOS”页签
            if (oosIssues.Count > 0)
            {
                Worksheet oosSheet = IQCResultTableWorkbook.Worksheets["OOS"];
                if (oosSheet == null)
                {
                    oosSheet = IQCResultTableWorkbook.Worksheets.Add("OOS");
                    oosSheet.Cells[0, 0].Value = "Project Code";
                    oosSheet.Cells[0, 1].Value = "Material Code";
                    oosSheet.Cells[0, 2].Value = "File/Sheet";
                    oosSheet.Cells[0, 3].Value = "Test Item";
                    oosSheet.Cells[0, 4].Value = "Test Value";
                    oosSheet.Cells[0, 5].Value = "Detail";
                    oosSheet.Cells[0, 6].Value = "Spec Upper Limit";
                    oosSheet.Cells[0, 7].Value = "Spec Lower Limit";
                }
                int rowIndex = oosSheet.Cells.MaxDataRow + 1;
                foreach (var issue in oosIssues)
                {
                    for (int i = 0; i < issue.Length; i++)
                    {
                        oosSheet.Cells[rowIndex, i].Value = issue[i];
                    }
                    rowIndex++;
                }
            }
        }

        private void CheckUnit(string project, string apn, string fileName, DataTable SpecTable, DataTable sheetDatatable, Workbook IQCResultTableWorkbook)
        {
            // 从规格表中筛选出与当前物料代码匹配的行
            DataRow[] DropSubRows = SpecTable.Select($"[Material Code]='{apn}'");

            // 从筛选出的行中获取所有 Test Item
            List<string> standardTestItems = new List<string>();
            foreach (DataRow row in DropSubRows)
            {
                if (row["Test Item"] != DBNull.Value)
                {
                    standardTestItems.Add(row["Test Item"].ToString());
                }
            }

            // 获取IQC测试数据中的所有列名（Test Item 是列名）
            List<string> iqcTestItems = new List<string>();
            foreach (DataColumn column in sheetDatatable.Columns)
            {
                iqcTestItems.Add(column.ColumnName);
            }

            List<object[]> unitIssues = new List<object[]>();

            foreach (var standardTestItem in standardTestItems)
            {
                if (iqcTestItems.Contains(standardTestItem))
                {
                    string specCQPItem = "";
                    string specUnit = "";
                    // 1. 提取Spec表CQP item列中()内的单位
                    foreach (DataRow row in DropSubRows)
                    {
                        if (row["Test Item"].ToString() == standardTestItem)
                        {
                            specCQPItem = row["CQP item"].ToString();
                            int left = specCQPItem.IndexOf('(');
                            int right = specCQPItem.IndexOf(')');
                            if (left >= 0 && right > left)
                            {
                                specUnit = specCQPItem.Substring(left + 1, right - left - 1).Trim();
                            }
                            break;
                        }
                    }

                    // 2. 获取待校验文件中第五行的D.测量单位
                    string actualUnit = "";
                    int columnIndex = sheetDatatable.Columns.IndexOf(standardTestItem);
                    if (columnIndex >= 0 && sheetDatatable.Rows.Count > 3)
                    {
                        // 第5行（索引4）为D.测量单位
                        actualUnit = sheetDatatable.Rows[3][columnIndex]?.ToString().Trim();
                    }

                    // 3. 比较单位是否一致
                    if (!string.IsNullOrEmpty(specUnit) && !string.IsNullOrEmpty(actualUnit) && specUnit != actualUnit)
                    {
                        // 4. 记录不一致项
                        unitIssues.Add(new object[] {
                                                project,
                                                apn,
                                                fileName,
                                                standardTestItem,
                                                specUnit,
                                                actualUnit
                                            });
                    }
                }
            }

            // 5. 输出到“单位”页签
            if (unitIssues.Count > 0)
            {
                Worksheet unitSheet = IQCResultTableWorkbook.Worksheets["单位"];
                if (unitSheet == null)
                {
                    unitSheet = IQCResultTableWorkbook.Worksheets.Add("单位");
                    unitSheet.Cells[0, 0].Value = "Project Code";
                    unitSheet.Cells[0, 1].Value = "Material Code";
                    unitSheet.Cells[0, 2].Value = "File/Sheet";
                    unitSheet.Cells[0, 3].Value = "Test Item";
                    unitSheet.Cells[0, 4].Value = "Spec单位";
                    unitSheet.Cells[0, 5].Value = "实际单位";
                }
                int rowIndex = unitSheet.Cells.MaxDataRow + 1;
                foreach (var issue in unitIssues)
                {
                    for (int i = 0; i < issue.Length; i++)
                    {
                        unitSheet.Cells[rowIndex, i].Value = issue[i];
                    }
                    rowIndex++;
                }
            }
        }

        // 解析IQC文件名中的APN集合
        private HashSet<string> ParseAPNs(string[] IQCFiles)
        {
            // 初始化一个HashSet来存储解析出的APN（唯一的物料代码）
            HashSet<string> apns = new HashSet<string>();

            try
            {
                // 遍历传入的IQC文件数组
                foreach (var IQCFile in IQCFiles)
                {
                    // 获取文件名（不包括路径）
                    string fileName = Path.GetFileName(IQCFile);

                    // 获取文件名去除扩展名的部分
                    string nameWithoutExtension = fileName.Substring(0, fileName.LastIndexOf('.'));

                    // 以'_'为分隔符拆分文件名
                    string[] parts = nameWithoutExtension.Split('_');

                    // 检查文件名拆分后的部分是否包含至少两个元素（确保有APN可用）
                    if (parts.Length >= 2)
                    {
                        // 获取第二部分作为APN（物料代码）
                        string apn = parts[1];

                        // 将APN添加到HashSet中，自动去重
                        apns.Add(apn);
                    }
                }
            }
            catch (Exception ex)
            {
                // 捕获异常并显示详细错误信息
                MessageBox.Show($"解析IQC文件名中的APN集合时发生错误: {ex.Message}\n{ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            // 返回包含所有唯一APN的HashSet
            return apns;
        }

        // 校验IQC文件夹路径和文件格式
        private bool CheckFolderAndFiles(string IQCFolderPath)
        {
            if (!System.IO.Directory.Exists(IQCFolderPath))
            {
                MessageBox.Show("IQC文件夹路径不存在！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            string[] allFiles = Directory.GetFiles(IQCFolderPath, "*", SearchOption.AllDirectories).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();
            foreach (var file in allFiles)
            {
                string ext = Path.GetExtension(file).ToLower();
                if (ext != ".xls" && ext != ".xlsx")
                {
                    MessageBox.Show($"检测到不支持的文件格式：{file}\n只允许xls/xlsx格式文件！", "文件格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            return true;
        }

        private bool CheckMESDataFilePattern(string MESDataFolderPath)
        {
            // 获取所有文件(排除.DS_Store文件)
            string[] allFiles = Directory.GetFiles(MESDataFolderPath).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();

            // 标准文件：X2953_MESData开头且以.xlsx结尾
            var specFilePattern = new System.Text.RegularExpressions.Regex(@"^X\d{4,}_MESData.*\.xlsx$", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // 非spec文件，全部视为待校验数据，必须是csv
            var nonSpecFiles = allFiles.Where(f => !specFilePattern.IsMatch(Path.GetFileName(f))).ToArray();
            var nonCsvTestFiles = nonSpecFiles.Where(f => !f.EndsWith(".csv", StringComparison.OrdinalIgnoreCase)).ToArray();

            if (nonCsvTestFiles.Length > 0)
            {
                string fileList = string.Join("\n", nonCsvTestFiles.Select(f => Path.GetFileName(f)));
                MessageBox.Show($"MES数据文件夹下存在非csv的原始测试文件：\n{fileList}\n请删除后重试！", "文件格式错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            
            return true;
        }

        private bool IsLimitMatch(string specLimit, string dataLimit)
        {
            // spec和data都为"NA"，一致
            if (specLimit == "NA" && dataLimit == "NA")
                return true;
            // 都为空字符串
            if (string.IsNullOrEmpty(specLimit) && string.IsNullOrEmpty(dataLimit))
                return true;
            // 都是数字，转成double比较
            if (double.TryParse(specLimit, out double specVal) && double.TryParse(dataLimit, out double dataVal))
                return Math.Abs(specVal - dataVal) < 0.0001;
            // 其他情况都不一致（包括spec为"NA"、data为""的情况）
            return false;
        }
        #endregion

        #region IPQC对比
        private void ProcessIPQCComparison(string IPQCFolderPath, string ResultPath, string ResultOutputPath)
        {
            // 获取标准文件
            string[] files = Directory.GetFiles(IPQCFolderPath).Where(f => Path.GetFileName(f) != ".DS_Store").ToArray();

            // 检查是否找到了标准文件
            if (files.Length == 0)
            {
                MessageBox.Show("IQC文件夹下未找到标准文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 获取Result模板文件
            string[] resultDirectories = Directory.GetFiles(ResultPath);
            string[] IPQCResultFile = resultDirectories.Where(item => item.Contains("IPQC")).ToArray();

            // 检查是否找到了IQC模板文件
            if (IPQCResultFile.Length == 0)
            {
                MessageBox.Show("未找到IPQC模板文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string IPQCResultFilePath = IPQCResultFile[0];

            // 设置Excel导出选项
            ExportTableOptions options = new ExportTableOptions
            {
                ExportColumnName = true,
                ExportAsString = true
            };

            // 加载标准文件的第一个工作簿
            Workbook SpecTableWorkbook = new Workbook(files[0]);

            // 将标准文件中每个工作表的数据保存到字典中
            Dictionary<string, DataTable> SpecSheetDataList = new Dictionary<string, DataTable>();
            foreach (Worksheet worksheet in SpecTableWorkbook.Worksheets)
            {
                string sheetName = worksheet.Name;
                DataTable sheetDatatable = worksheet.Cells.ExportDataTable(0, 0, worksheet.Cells.MaxDataRow + 1, worksheet.Cells.MaxDataColumn + 1, options);
                SpecSheetDataList.Add(sheetName, sheetDatatable);
            }

            // 文件夹下的所有二级测试文件夹
            string[] IPQCDirectories = Directory.GetDirectories(IPQCFolderPath);
        }
        #endregion
    }
}
